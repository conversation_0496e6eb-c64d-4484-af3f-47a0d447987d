"""
上传配置模块
"""
import os
from pydantic_settings import BaseSettings
from typing import List


class UploadConfig(BaseSettings):
    """上传配置"""
    
    prefix: str = '/profile'
    path: str = 'storage/uploads'
    machine: str = 'A'
    allowed_extensions: List[str] = [
        # 图片
        'bmp', 'gif', 'jpg', 'jpeg', 'png',
        # 文件
        'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'html', 'htm', 'txt',
        # 压缩文件
        'rar', 'zip', 'gz', 'bz2',
        # 视频格式
        'mp4', 'avi', 'rmvb',
        # pdf
        'pdf',
    ]
    download_path: str = 'storage/downloads'
    
    def __post_init__(self):
        """初始化后创建目录"""
        if not os.path.exists(self.path):
            os.makedirs(self.path)
        if not os.path.exists(self.download_path):
            os.makedirs(self.download_path)
    
    class Config:
        env_prefix = 'UPLOAD_'
        env_file = '.env'
