from datetime import datetime, time
from sqlalchemy import and_, delete, desc, func, or_, select, update  # noqa: F401
from sqlalchemy.ext.asyncio import AsyncSession
from apps.admin.models.dept import SysDeptModel
from apps.admin.models.menu import SysMenuModel
from apps.admin.models.role import SysRoleModel, SysRoleMenuModel, SysRoleDeptModel
from apps.admin.models.user import SysUserModel, SysUserRoleModel
from apps.admin.schemas.role import RoleDeptSchema, RoleMenuSchema, RoleSchema, RolePageQuerySchema
from utils.page import PageUtil


class RoleCrud:
    """
    角色管理模块数据库操作层
    """

    @classmethod
    async def get_role_by_name(cls, db: AsyncSession, role_name: str):
        """
        根据角色名获取在用角色信息

        :param db: orm对象
        :param role_name: 角色名
        :return: 当前角色名的角色信息对象
        """
        query_role_info = (
            (
                await db.execute(
                    select(SysRoleModel)
                    .where(SysRoleModel.status == '0', SysRoleModel.del_flag == '0', SysRoleModel.role_name == role_name)
                    .order_by(desc(SysRoleModel.create_time))
                    .distinct()
                )
            )
            .scalars()
            .first()
        )

        return query_role_info

    @classmethod
    async def get_role_by_info(cls, db: AsyncSession, role: RoleSchema):
        """
        根据角色参数获取角色信息

        :param db: orm对象
        :param role: 角色参数
        :return: 当前角色参数的角色信息对象
        """
        query_role_info = (
            (
                await db.execute(
                    select(SysRoleModel)
                    .where(
                        SysRoleModel.del_flag == '0',
                        SysRoleModel.role_name == role.role_name if role.role_name else True,
                        SysRoleModel.role_key == role.role_key if role.role_key else True,
                    )
                    .order_by(desc(SysRoleModel.create_time))
                    .distinct()
                )
            )
            .scalars()
            .first()
        )

        return query_role_info

    @classmethod
    async def get_role_by_id(cls, db: AsyncSession, role_id: int):
        """
        根据角色id获取在用角色信息

        :param db: orm对象
        :param role_id: 角色id
        :return: 当前角色id的角色信息对象
        """
        role_info = (
            (
                await db.execute(
                    select(SysRoleModel).where(SysRoleModel.role_id == role_id, SysRoleModel.status == '0', SysRoleModel.del_flag == '0')
                )
            )
            .scalars()
            .first()
        )

        return role_info

    @classmethod
    async def get_role_detail_by_id(cls, db: AsyncSession, role_id: int):
        """
        根据role_id获取角色详细信息

        :param db: orm对象
        :param role_id: 角色id
        :return: 当前role_id的角色信息对象
        """
        query_role_info = (
            (await db.execute(select(SysRoleModel).where(SysRoleModel.del_flag == '0', SysRoleModel.role_id == role_id).distinct()))
            .scalars()
            .first()
        )

        return query_role_info

    @classmethod
    async def get_role_select_option_dao(cls, db: AsyncSession):
        """
        获取编辑页面对应的在用角色列表信息

        :param db: orm对象
        :return: 角色列表信息
        """
        role_info = (
            (
                await db.execute(
                    select(SysRoleModel).where(SysRoleModel.role_id != 1, SysRoleModel.status == '0', SysRoleModel.del_flag == '0')
                )
            )
            .scalars()
            .all()
        )

        return role_info

    @classmethod
    async def get_role_list(
        cls, db: AsyncSession, query_object: RolePageQuerySchema, data_scope_sql: str, is_page: bool = False
    ):
        """
        根据查询参数获取角色列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param data_scope_sql: 数据权限对应的查询sql语句
        :param is_page: 是否开启分页
        :return: 角色列表信息对象
        """
        query = (
            select(SysRoleModel)
            .join(SysUserRoleModel, SysUserRoleModel.role_id == SysRoleModel.role_id, isouter=True)
            .join(SysUserModel, SysUserModel.user_id == SysUserRoleModel.user_id, isouter=True)
            .join(SysDeptModel, SysDeptModel.dept_id == SysUserModel.dept_id, isouter=True)
            .where(
                SysRoleModel.del_flag == '0',
                SysRoleModel.role_id == query_object.role_id if query_object.role_id is not None else True,
                SysRoleModel.role_name.like(f'%{query_object.role_name}%') if query_object.role_name else True,
                SysRoleModel.role_key.like(f'%{query_object.role_key}%') if query_object.role_key else True,
                SysRoleModel.status == query_object.status if query_object.status else True,
                SysRoleModel.create_time.between(
                    datetime.combine(datetime.strptime(query_object.begin_time, '%Y-%m-%d'), time(00, 00, 00)),
                    datetime.combine(datetime.strptime(query_object.end_time, '%Y-%m-%d'), time(23, 59, 59)),
                )
                if query_object.begin_time and query_object.end_time
                else True,
                eval(data_scope_sql),
            )
            .order_by(SysRoleModel.role_sort)
            .distinct()
        )
        role_list = await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size, is_page)

        return role_list

    @classmethod
    async def add_role_dao(cls, db: AsyncSession, role: RoleSchema):
        """
        新增角色数据库操作

        :param db: orm对象
        :param role: 角色对象
        :return:
        """
        db_role = SysRoleModel(**role.model_dump(exclude={'admin'}))
        db.add(db_role)
        await db.flush()

        return db_role

    @classmethod
    async def edit_role_dao(cls, db: AsyncSession, role: dict):
        """
        编辑角色数据库操作

        :param db: orm对象
        :param role: 需要更新的角色字典
        :return:
        """
        await db.execute(update(SysRoleModel), [role])

    @classmethod
    async def delete_role_dao(cls, db: AsyncSession, role: RoleSchema):
        """
        删除角色数据库操作

        :param db: orm对象
        :param role: 角色对象
        :return:
        """
        await db.execute(
            update(SysRoleModel)
            .where(SysRoleModel.role_id == role.role_id)
            .values(del_flag='2', update_by=role.update_by, update_time=role.update_time)
        )

    @classmethod
    async def get_role_menu_dao(cls, db: AsyncSession, role: RoleSchema):
        """
        根据角色id获取角色菜单关联列表信息

        :param db: orm对象
        :param role: 角色对象
        :return: 角色菜单关联列表信息
        """
        role_menu_query_all = (
            (
                await db.execute(
                    select(SysMenuModel)
                    .join(SysRoleMenuModel, SysRoleMenuModel.menu_id == SysMenuModel.menu_id)
                    .where(
                        SysRoleMenuModel.role_id == role.role_id,
                        ~SysMenuModel.menu_id.in_(
                            select(SysMenuModel.parent_id)
                            .select_from(SysMenuModel)
                            .join(
                                SysRoleMenuModel,
                                and_(SysRoleMenuModel.menu_id == SysMenuModel.menu_id, SysRoleMenuModel.role_id == role.role_id),
                            )
                        )
                        if role.menu_check_strictly
                        else True,
                    )
                    .order_by(SysMenuModel.parent_id, SysMenuModel.order_num)
                )
            )
            .scalars()
            .all()
        )

        return role_menu_query_all

    @classmethod
    async def add_role_menu_dao(cls, db: AsyncSession, role_menu: RoleMenuSchema):
        """
        新增角色菜单关联信息数据库操作

        :param db: orm对象
        :param role_menu: 用户角色菜单关联对象
        :return:
        """
        db_role_menu = SysRoleMenuModel(**role_menu.model_dump())
        db.add(db_role_menu)

    @classmethod
    async def delete_role_menu_dao(cls, db: AsyncSession, role_menu: RoleMenuSchema):
        """
        删除角色菜单关联信息数据库操作

        :param db: orm对象
        :param role_menu: 角色菜单关联对象
        :return:
        """
        await db.execute(delete(SysRoleMenuModel).where(SysRoleMenuModel.role_id.in_([role_menu.role_id])))

    @classmethod
    async def get_role_dept_dao(cls, db: AsyncSession, role: RoleSchema):
        """
        根据角色id获取角色部门关联列表信息

        :param db: orm对象
        :param role: 角色对象
        :return: 角色部门关联列表信息
        """
        role_dept_query_all = (
            (
                await db.execute(
                    select(SysDeptModel)
                    .join(SysRoleDeptModel, SysRoleDeptModel.dept_id == SysDeptModel.dept_id)
                    .where(
                        SysRoleDeptModel.role_id == role.role_id,
                        ~SysDeptModel.dept_id.in_(
                            select(SysDeptModel.parent_id)
                            .select_from(SysDeptModel)
                            .join(
                                SysRoleDeptModel,
                                and_(SysRoleDeptModel.dept_id == SysDeptModel.dept_id, SysRoleDeptModel.role_id == role.role_id),
                            )
                        )
                        if role.dept_check_strictly
                        else True,
                    )
                    .order_by(SysDeptModel.parent_id, SysDeptModel.order_num)
                )
            )
            .scalars()
            .all()
        )

        return role_dept_query_all

    @classmethod
    async def add_role_dept_dao(cls, db: AsyncSession, role_dept: RoleDeptSchema):
        """
        新增角色部门关联信息数据库操作

        :param db: orm对象
        :param role_dept: 用户角色部门关联对象
        :return:
        """
        db_role_dept = SysRoleDeptModel(**role_dept.dict())
        db.add(db_role_dept)

    @classmethod
    async def delete_role_dept_dao(cls, db: AsyncSession, role_dept: RoleDeptSchema):
        """
        删除角色部门关联信息数据库操作

        :param db: orm对象
        :param role_dept: 角色部门关联对象
        :return:
        """
        await db.execute(delete(SysRoleDeptModel).where(SysRoleDeptModel.role_id.in_([role_dept.role_id])))

    @classmethod
    async def count_user_role_dao(cls, db: AsyncSession, role_id: int):
        """
        根据角色id查询角色关联用户数量

        :param db: orm对象
        :param role_id: 角色id
        :return: 角色关联用户数量
        """
        user_count = (
            await db.execute(select(func.count('*')).select_from(SysUserRoleModel).where(SysUserRoleModel.role_id == role_id))
        ).scalar()

        return user_count
