from datetime import datetime, time
from sqlalchemy import asc, delete, desc, select
from sqlalchemy.ext.asyncio import AsyncSession
from apps.admin.models.log import SysLogininforModel, SysOperLogModel
from apps.admin.schemas.log import LogininforSchema, LoginLogPageQuerySchema, OperLogSchema, OperLogPageQuerySchema
from utils.common import SnakeCaseUtil
from utils.page import PageUtil
from utils.time_format import TimeFormatUtil


class OperationLogCurd:
    """
    操作日志管理模块数据库操作层
    """

    @classmethod
    async def get_operation_log_list(cls, db: AsyncSession, query_object: OperLogPageQuerySchema, is_page: bool = False):
        """
        根据查询参数获取操作日志列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 操作日志列表信息对象
        """
        if query_object.is_asc == 'ascending':
            order_by_column = asc(getattr(SysOperLogModel, SnakeCaseUtil.camel_to_snake(query_object.order_by_column), None))
        elif query_object.is_asc == 'descending':
            order_by_column = desc(
                getattr(SysOperLogModel, SnakeCaseUtil.camel_to_snake(query_object.order_by_column), None)
            )
        else:
            order_by_column = desc(SysOperLogModel.oper_time)
        query = (
            select(SysOperLogModel)
            .where(
                SysOperLogModel.title.like(f'%{query_object.title}%') if query_object.title else True,
                SysOperLogModel.oper_name.like(f'%{query_object.oper_name}%') if query_object.oper_name else True,
                SysOperLogModel.business_type == query_object.business_type if query_object.business_type else True,
                SysOperLogModel.status == query_object.status if query_object.status else True,
                SysOperLogModel.oper_time.between(
                    datetime.combine(TimeFormatUtil.parse_date(query_object.begin_time), time(00, 00, 00)),
                    datetime.combine(TimeFormatUtil.parse_date(query_object.end_time), time(23, 59, 59)),
                )
                if query_object.begin_time and query_object.end_time
                else True,
            )
            .distinct()
            .order_by(order_by_column)
        )
        operation_log_list = await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size, is_page)

        return operation_log_list

    @classmethod
    async def add_operation_log_dao(cls, db: AsyncSession, operation_log: OperLogSchema):
        """
        新增操作日志数据库操作

        :param db: orm对象
        :param operation_log: 操作日志对象
        :return: 新增校验结果
        """
        db_operation_log = SysOperLogModel(**operation_log.model_dump())
        db.add(db_operation_log)
        await db.flush()

        return db_operation_log

    @classmethod
    async def delete_operation_log_dao(cls, db: AsyncSession, operation_log: OperLogSchema):
        """
        删除操作日志数据库操作

        :param db: orm对象
        :param operation_log: 操作日志对象
        :return:
        """
        await db.execute(delete(SysOperLogModel).where(SysOperLogModel.oper_id.in_([operation_log.oper_id])))

    @classmethod
    async def clear_operation_log_dao(cls, db: AsyncSession):
        """
        清除操作日志数据库操作

        :param db: orm对象
        :return:
        """
        await db.execute(delete(SysOperLogModel))


class LoginLogCrud:
    """
    登录日志管理模块数据库操作层
    """

    @classmethod
    async def get_login_log_list(cls, db: AsyncSession, query_object: LoginLogPageQuerySchema, is_page: bool = False):
        """
        根据查询参数获取登录日志列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 登录日志列表信息对象
        """
        if query_object.is_asc == 'ascending':
            order_by_column = asc(
                getattr(SysLogininforModel, SnakeCaseUtil.camel_to_snake(query_object.order_by_column), None)
            )
        elif query_object.is_asc == 'descending':
            order_by_column = desc(
                getattr(SysLogininforModel, SnakeCaseUtil.camel_to_snake(query_object.order_by_column), None)
            )
        else:
            order_by_column = desc(SysLogininforModel.login_time)
        query = (
            select(SysLogininforModel)
            .where(
                SysLogininforModel.ipaddr.like(f'%{query_object.ipaddr}%') if query_object.ipaddr else True,
                SysLogininforModel.user_name.like(f'%{query_object.user_name}%') if query_object.user_name else True,
                SysLogininforModel.status == query_object.status if query_object.status else True,
                SysLogininforModel.login_time.between(
                    datetime.combine(TimeFormatUtil.parse_date(query_object.begin_time), time(00, 00, 00)),
                    datetime.combine(TimeFormatUtil.parse_date(query_object.end_time), time(23, 59, 59)),
                )
                if query_object.begin_time and query_object.end_time
                else True,
            )
            .distinct()
            .order_by(order_by_column)
        )
        login_log_list = await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size, is_page)

        return login_log_list

    @classmethod
    async def add_login_log_dao(cls, db: AsyncSession, login_log: LogininforSchema):
        """
        新增登录日志数据库操作

        :param db: orm对象
        :param login_log: 登录日志对象
        :return: 新增校验结果
        """
        db_login_log = SysLogininforModel(**login_log.model_dump())
        db.add(db_login_log)
        await db.flush()

        return db_login_log

    @classmethod
    async def delete_login_log_dao(cls, db: AsyncSession, login_log: LogininforSchema):
        """
        删除登录日志数据库操作

        :param db: orm对象
        :param login_log: 登录日志对象
        :return:
        """
        await db.execute(delete(SysLogininforModel).where(SysLogininforModel.info_id.in_([login_log.info_id])))

    @classmethod
    async def clear_login_log_dao(cls, db: AsyncSession):
        """
        清除登录日志数据库操作

        :param db: orm对象
        :return:
        """
        await db.execute(delete(SysLogininforModel))
