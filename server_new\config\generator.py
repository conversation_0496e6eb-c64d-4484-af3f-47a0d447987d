"""
代码生成配置模块
"""
import os
from pydantic_settings import BaseSettings


class GeneratorConfig(BaseSettings):
    """代码生成配置"""
    
    author: str = 'mengqb'
    package_name: str = 'admin.system'
    auto_remove_pre: bool = False
    table_prefix: str = 'sys_'
    allow_overwrite: bool = False
    gen_path: str = 'storage/generated'
    
    def __post_init__(self):
        """初始化后创建目录"""
        if not os.path.exists(self.gen_path):
            os.makedirs(self.gen_path)
    
    class Config:
        env_prefix = 'GEN_'
        env_file = '.env'
