import uvicorn
from contextlib import asynccontextmanager
from fastapi import FastAPI
from config.settings import AppConfig
from core.database.sql_manager import init_create_table
from core.database.redis_manager import RedisManager
from core.scheduler.scheduler_manager import SchedulerUtil
from core.exception.handle import handle_exception
from core.middleware.handle import handle_middleware
from apps.admin.routers.cache import router as cache_router
from apps.admin.routers.captcha import router as captcha_router
from apps.admin.routers.common import router as common_router
from apps.admin.routers.config import router as config_router
from apps.admin.routers.dept import router as dept_router
from apps.admin.routers.dict import router as dict_router
from apps.admin.routers.log import router as log_router
from apps.admin.routers.login import router as login_router
from apps.admin.routers.job import router as job_router
from apps.admin.routers.menu import router as menu_router
from apps.admin.routers.notice import router as notice_router
from apps.admin.routers.online import router as online_router
from apps.admin.routers.post import router as post_router
from apps.admin.routers.role import router as role_router
from apps.admin.routers.server import router as server_router
from apps.admin.routers.user import router as user_router
from apps.generator.routers.gen import router as gen_router
from core.sub_application.handle import handle_sub_applications
from utils.common import worship
from core.log.log_manager import logger


# 生命周期事件
@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info(f'{AppConfig.app_name}开始启动')

    worship()

    await init_create_table()  # 表格存在时不会重复创建
    logger.info(f'连接数据库成功')

    app.state.redis = await RedisManager.create_redis_pool()
    await RedisManager.init_sys_dict(app.state.redis)
    await RedisManager.init_sys_config(app.state.redis)
    await SchedulerUtil.init_system_scheduler()
    logger.info(f'{AppConfig.app_name}启动成功')
    yield
    await RedisManager.close_redis_pool(app)
    await SchedulerUtil.close_system_scheduler()


# 初始化FastAPI对象
app = FastAPI(
    title=AppConfig.app_name,
    description=f'{AppConfig.app_name}接口文档',
    version=AppConfig.app_version,
    lifespan=lifespan,
)

# 挂载子应用
handle_sub_applications(app)
# 加载中间件处理方法
handle_middleware(app)
# 加载全局异常处理方法
handle_exception(app)


# 加载路由列表
router_list = [
    {'router': login_router, 'tags': ['登录模块']},
    {'router': captcha_router, 'tags': ['验证码模块']},
    {'router': user_router, 'tags': ['系统管理-用户管理']},
    {'router': role_router, 'tags': ['系统管理-角色管理']},
    {'router': menu_router, 'tags': ['系统管理-菜单管理']},
    {'router': dept_router, 'tags': ['系统管理-部门管理']},
    {'router': post_router, 'tags': ['系统管理-岗位管理']},
    {'router': dict_router, 'tags': ['系统管理-字典管理']},
    {'router': config_router, 'tags': ['系统管理-参数管理']},
    {'router': notice_router, 'tags': ['系统管理-通知公告管理']},
    {'router': log_router, 'tags': ['系统管理-日志管理']},
    {'router': online_router, 'tags': ['系统监控-在线用户']},
    {'router': job_router, 'tags': ['系统监控-定时任务']},
    {'router': server_router, 'tags': ['系统监控-菜单管理']},
    {'router': cache_router, 'tags': ['系统监控-缓存监控']},
    {'router': common_router, 'tags': ['通用模块']},
    {'router': gen_router, 'tags': ['代码生成']},
]

for router in router_list:
    app.include_router(router=router.get('router'), tags=router.get('tags'))


if __name__ == '__main__':
    uvicorn.run(
        app='main:app',  # 更新为使用main.py
        host=AppConfig.app_host,
        port=AppConfig.app_port,
        root_path=AppConfig.app_root_path,
        reload=AppConfig.app_reload,
    )
