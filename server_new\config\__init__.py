"""
统一配置管理模块
"""
import argparse
import os
import sys
from dotenv import load_dotenv
from functools import lru_cache

from .app import AppConfig as _AppConfig
from .database import DatabaseConfig as _DatabaseConfig
from .redis import RedisConfig as _RedisConfig
from .jwt import JWTConfig as _JWTConfig
from .upload import UploadConfig as _UploadConfig
from .generator import GeneratorConfig as _GeneratorConfig


class Settings:
    """统一配置管理器"""

    def __init__(self):
        self._parse_cli_args()
        self._app = None
        self._database = None
        self._redis = None
        self._jwt = None
        self._upload = None
        self._generator = None

    @property
    @lru_cache()
    def app(self) -> _AppConfig:
        """获取应用配置"""
        if self._app is None:
            self._app = _AppConfig()
        return self._app

    @property
    @lru_cache()
    def database(self) -> _DatabaseConfig:
        """获取数据库配置"""
        if self._database is None:
            self._database = _DatabaseConfig()
        return self._database

    @property
    @lru_cache()
    def redis(self) -> _RedisConfig:
        """获取Redis配置"""
        if self._redis is None:
            self._redis = _RedisConfig()
        return self._redis

    @property
    @lru_cache()
    def jwt(self) -> _JWTConfig:
        """获取JWT配置"""
        if self._jwt is None:
            self._jwt = _JWTConfig()
        return self._jwt

    @property
    @lru_cache()
    def upload(self) -> _UploadConfig:
        """获取上传配置"""
        if self._upload is None:
            self._upload = _UploadConfig()
        return self._upload

    @property
    @lru_cache()
    def generator(self) -> _GeneratorConfig:
        """获取代码生成配置"""
        if self._generator is None:
            self._generator = _GeneratorConfig()
        return self._generator

    @staticmethod
    def _parse_cli_args():
        """解析命令行参数"""
        if 'uvicorn' in sys.argv[0]:
            # 使用uvicorn启动时，命令行参数需要按照uvicorn的文档进行配置，无法自定义参数
            pass
        else:
            # 使用argparse定义命令行参数
            parser = argparse.ArgumentParser(description='命令行参数')
            parser.add_argument('--env', type=str, default='', help='运行环境')
            # 解析命令行参数
            args = parser.parse_args()
            # 设置环境变量，如果未设置命令行参数，默认APP_ENV为dev
            os.environ['APP_ENV'] = args.env if args.env else 'dev'

        # 读取运行环境
        run_env = os.environ.get('APP_ENV', '')
        # 运行环境未指定时默认加载.env.dev
        env_file = '.env.dev'
        # 运行环境不为空时按命令行参数加载对应.env文件
        if run_env != '':
            env_file = f'.env.{run_env}'
        # 加载配置
        load_dotenv(env_file)


# 全局配置实例
settings = Settings()

# 向后兼容的配置别名
AppConfig = settings.app
DatabaseConfig = settings.database
RedisConfig = settings.redis
JWTConfig = settings.jwt
UploadConfig = settings.upload
GeneratorConfig = settings.generator