from sqlalchemy import and_, select
from sqlalchemy.ext.asyncio import AsyncSession
from apps.admin.models.dept import SysDeptModel
from apps.admin.models.user import SysUserModel


async def login_by_account(db: AsyncSession, user_name: str):
    """
    根据用户名查询用户信息

    :param db: orm对象
    :param user_name: 用户名
    :return: 用户对象
    """
    user = (
        await db.execute(
            select(SysUserModel, SysDeptModel)
            .where(SysUserModel.user_name == user_name, SysUserModel.del_flag == '0')
            .join(
                SysDeptModel,
                and_(SysUserModel.dept_id == SysDeptModel.dept_id, SysDeptModel.status == '0', SysDeptModel.del_flag == '0'),
                isouter=True,
            )
            .distinct()
        )
    ).first()

    return user
