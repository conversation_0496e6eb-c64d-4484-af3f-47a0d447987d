"""
应用工厂模块
"""
from contextlib import asynccontextmanager
from fastapi import FastAPI

from config import settings
from .database.manager import DatabaseManager
from .database.redis_manager import RedisManager
from .scheduler.scheduler_manager import SchedulerService
from .exceptions.handler import setup_exception_handlers
from .middleware.handler import setup_middleware
from .routing import RouterRegistry
from .sub_application.handler import setup_sub_applications
from .logging.manager import logger


class ApplicationFactory:
    """应用工厂类，负责创建和配置FastAPI应用"""
    
    @classmethod
    def create_app(cls) -> FastAPI:
        """创建FastAPI应用实例"""
        app = FastAPI(
            title=settings.app.name,
            description=f'{settings.app.name}接口文档',
            version=settings.app.version,
            lifespan=cls._lifespan,
        )
        
        cls._setup_middleware(app)
        cls._setup_exception_handlers(app)
        cls._setup_routers(app)
        cls._setup_sub_applications(app)
        
        return app
    
    @classmethod
    @asynccontextmanager
    async def _lifespan(cls, app: FastAPI):
        """应用生命周期管理"""
        logger.info(f'{settings.app.name}开始启动')
        
        # 初始化数据库
        await DatabaseManager.init_database()
        logger.info('连接数据库成功')
        
        # 初始化Redis
        app.state.redis = await RedisManager.create_redis_pool()
        await RedisManager.init_sys_dict(app.state.redis)
        await RedisManager.init_sys_config(app.state.redis)
        
        # 初始化调度器
        await SchedulerService.init_system_scheduler()
        
        logger.info(f'{settings.app.name}启动成功')
        
        yield
        
        # 清理资源
        await RedisManager.close_redis_pool(app)
        await SchedulerService.close_system_scheduler()
        logger.info(f'{settings.app.name}关闭完成')
    
    @classmethod
    def _setup_middleware(cls, app: FastAPI):
        """设置中间件"""
        setup_middleware(app)
    
    @classmethod
    def _setup_exception_handlers(cls, app: FastAPI):
        """设置异常处理器"""
        setup_exception_handlers(app)
    
    @classmethod
    def _setup_routers(cls, app: FastAPI):
        """设置路由"""
        RouterRegistry.register_all_routers(app)
    
    @classmethod
    def _setup_sub_applications(cls, app: FastAPI):
        """设置子应用"""
        setup_sub_applications(app)
