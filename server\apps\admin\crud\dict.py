from datetime import datetime, time
from sqlalchemy import and_, delete, func, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from apps.admin.models.dict import SysDictTypeModel, SysDictDataModel
from apps.admin.schemas.dict import DictDataSchema, DictDataPageQuerySchema, DictTypeSchema, DictTypePageQuerySchema
from utils.page import PageUtil
from utils.time_format import list_format_datetime


class DictTypeCrud:
    """
    字典类型管理模块数据库操作层
    """

    @classmethod
    async def get_dict_type_detail_by_id(cls, db: AsyncSession, dict_id: int):
        """
        根据字典类型id获取字典类型详细信息

        :param db: orm对象
        :param dict_id: 字典类型id
        :return: 字典类型信息对象
        """
        dict_type_info = (await db.execute(select(SysDictTypeModel).where(SysDictTypeModel.dict_id == dict_id))).scalars().first()

        return dict_type_info

    @classmethod
    async def get_dict_type_detail_by_info(cls, db: AsyncSession, dict_type: DictTypeSchema):
        """
        根据字典类型参数获取字典类型信息

        :param db: orm对象
        :param dict_type: 字典类型参数对象
        :return: 字典类型信息对象
        """
        dict_type_info = (
            (
                await db.execute(
                    select(SysDictTypeModel).where(
                        SysDictTypeModel.dict_type == dict_type.dict_type if dict_type.dict_type else True,
                        SysDictTypeModel.dict_name == dict_type.dict_name if dict_type.dict_name else True,
                    )
                )
            )
            .scalars()
            .first()
        )

        return dict_type_info

    @classmethod
    async def get_all_dict_type(cls, db: AsyncSession):
        """
        获取所有的字典类型信息

        :param db: orm对象
        :return: 字典类型信息列表对象
        """
        dict_type_info = (await db.execute(select(SysDictTypeModel))).scalars().all()

        return list_format_datetime(dict_type_info)

    @classmethod
    async def get_dict_type_list(cls, db: AsyncSession, query_object: DictTypePageQuerySchema, is_page: bool = False):
        """
        根据查询参数获取字典类型列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 字典类型列表信息对象
        """
        query = (
            select(SysDictTypeModel)
            .where(
                SysDictTypeModel.dict_name.like(f'%{query_object.dict_name}%') if query_object.dict_name else True,
                SysDictTypeModel.dict_type.like(f'%{query_object.dict_type}%') if query_object.dict_type else True,
                SysDictTypeModel.status == query_object.status if query_object.status else True,
                SysDictTypeModel.create_time.between(
                    datetime.combine(datetime.strptime(query_object.begin_time, '%Y-%m-%d'), time(00, 00, 00)),
                    datetime.combine(datetime.strptime(query_object.end_time, '%Y-%m-%d'), time(23, 59, 59)),
                )
                if query_object.begin_time and query_object.end_time
                else True,
            )
            .order_by(SysDictTypeModel.dict_id)
            .distinct()
        )
        dict_type_list = await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size, is_page)

        return dict_type_list

    @classmethod
    async def add_dict_type_dao(cls, db: AsyncSession, dict_type: DictTypeSchema):
        """
        新增字典类型数据库操作

        :param db: orm对象
        :param dict_type: 字典类型对象
        :return:
        """
        db_dict_type = SysDictTypeModel(**dict_type.model_dump())
        db.add(db_dict_type)
        await db.flush()

        return db_dict_type

    @classmethod
    async def edit_dict_type_dao(cls, db: AsyncSession, dict_type: dict):
        """
        编辑字典类型数据库操作

        :param db: orm对象
        :param dict_type: 需要更新的字典类型字典
        :return:
        """
        await db.execute(update(SysDictTypeModel), [dict_type])

    @classmethod
    async def delete_dict_type_dao(cls, db: AsyncSession, dict_type: DictTypeSchema):
        """
        删除字典类型数据库操作

        :param db: orm对象
        :param dict_type: 字典类型对象
        :return:
        """
        await db.execute(delete(SysDictTypeModel).where(SysDictTypeModel.dict_id.in_([dict_type.dict_id])))


class DictDataCrud:
    """
    字典数据管理模块数据库操作层
    """

    @classmethod
    async def get_dict_data_detail_by_id(cls, db: AsyncSession, dict_code: int):
        """
        根据字典数据id获取字典数据详细信息

        :param db: orm对象
        :param dict_code: 字典数据id
        :return: 字典数据信息对象
        """
        dict_data_info = (
            (await db.execute(select(SysDictDataModel).where(SysDictDataModel.dict_code == dict_code))).scalars().first()
        )

        return dict_data_info

    @classmethod
    async def get_dict_data_detail_by_info(cls, db: AsyncSession, dict_data: DictDataSchema):
        """
        根据字典数据参数获取字典数据信息

        :param db: orm对象
        :param dict_data: 字典数据参数对象
        :return: 字典数据信息对象
        """
        dict_data_info = (
            (
                await db.execute(
                    select(SysDictDataModel).where(
                        SysDictDataModel.dict_type == dict_data.dict_type,
                        SysDictDataModel.dict_label == dict_data.dict_label,
                        SysDictDataModel.dict_value == dict_data.dict_value,
                    )
                )
            )
            .scalars()
            .first()
        )

        return dict_data_info

    @classmethod
    async def get_dict_data_list(cls, db: AsyncSession, query_object: DictDataPageQuerySchema, is_page: bool = False):
        """
        根据查询参数获取字典数据列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 字典数据列表信息对象
        """
        query = (
            select(SysDictDataModel)
            .where(
                SysDictDataModel.dict_type == query_object.dict_type if query_object.dict_type else True,
                SysDictDataModel.dict_label.like(f'%{query_object.dict_label}%') if query_object.dict_label else True,
                SysDictDataModel.status == query_object.status if query_object.status else True,
            )
            .order_by(SysDictDataModel.dict_sort)
            .distinct()
        )
        dict_data_list = await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size, is_page)

        return dict_data_list

    @classmethod
    async def query_dict_data_list(cls, db: AsyncSession, dict_type: str):
        """
        根据查询参数获取字典数据列表信息

        :param db: orm对象
        :param dict_type: 字典类型
        :return: 字典数据列表信息对象
        """
        dict_data_list = (
            (
                await db.execute(
                    select(SysDictDataModel)
                    .select_from(SysDictTypeModel)
                    .where(SysDictTypeModel.dict_type == dict_type if dict_type else True, SysDictTypeModel.status == '0')
                    .join(
                        SysDictDataModel,
                        and_(SysDictTypeModel.dict_type == SysDictDataModel.dict_type, SysDictDataModel.status == '0'),
                        isouter=True,
                    )
                    .order_by(SysDictDataModel.dict_sort)
                    .distinct()
                )
            )
            .scalars()
            .all()
        )

        return dict_data_list

    @classmethod
    async def add_dict_data_dao(cls, db: AsyncSession, dict_data: DictDataSchema):
        """
        新增字典数据数据库操作

        :param db: orm对象
        :param dict_data: 字典数据对象
        :return:
        """
        db_data_type = SysDictDataModel(**dict_data.model_dump())
        db.add(db_data_type)
        await db.flush()

        return db_data_type

    @classmethod
    async def edit_dict_data_dao(cls, db: AsyncSession, dict_data: dict):
        """
        编辑字典数据数据库操作

        :param db: orm对象
        :param dict_data: 需要更新的字典数据字典
        :return:
        """
        await db.execute(update(SysDictDataModel), [dict_data])

    @classmethod
    async def delete_dict_data_dao(cls, db: AsyncSession, dict_data: DictDataSchema):
        """
        删除字典数据数据库操作

        :param db: orm对象
        :param dict_data: 字典数据对象
        :return:
        """
        await db.execute(delete(SysDictDataModel).where(SysDictDataModel.dict_code.in_([dict_data.dict_code])))

    @classmethod
    async def count_dict_data_dao(cls, db: AsyncSession, dict_type: str):
        """
        根据字典类型查询字典类型关联的字典数据数量

        :param db: orm对象
        :param dict_type: 字典类型
        :return: 字典类型关联的字典数据数量
        """
        dict_data_count = (
            await db.execute(select(func.count('*')).select_from(SysDictDataModel).where(SysDictDataModel.dict_type == dict_type))
        ).scalar()

        return dict_data_count
